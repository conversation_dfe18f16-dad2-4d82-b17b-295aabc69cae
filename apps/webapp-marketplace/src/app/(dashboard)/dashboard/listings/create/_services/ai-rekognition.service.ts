/**
 * Mock AWS Rekognition Service for AI-powered listing creation
 * This service simulates AWS Rekognition responses for image analysis
 */

export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
  ParentName?: string;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Confidence: number;
  Type: 'LINE' | 'WORD';
}

export interface RekognitionFaceDetail {
  BoundingBox: {
    Width: number;
    Height: number;
    Left: number;
    Top: number;
  };
  Confidence: number;
}

export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

/**
 * Mock data for different types of items
 */
const MOCK_RESPONSES: Record<string, RekognitionResponse> = {
  laptop: {
    Labels: [
      { Name: 'Laptop', Confidence: 95.5 },
      { Name: 'Computer', Confidence: 92.3 },
      { Name: 'Electronics', Confidence: 89.7 },
      { Name: 'Device', Confidence: 87.2 },
      { Name: 'Technology', Confidence: 85.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
      { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
      { DetectedText: '13-inch', Confidence: 91.8, Type: 'WORD' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Good',
  },
  phone: {
    Labels: [
      { Name: 'Mobile Phone', Confidence: 97.8 },
      { Name: 'Smartphone', Confidence: 95.2 },
      { Name: 'Electronics', Confidence: 93.1 },
      { Name: 'Communication Device', Confidence: 88.9 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'iPhone 14', Confidence: 96.7, Type: 'LINE' },
      { DetectedText: 'Pro Max', Confidence: 94.3, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Like New',
  },
  furniture: {
    Labels: [
      { Name: 'Furniture', Confidence: 94.2 },
      { Name: 'Chair', Confidence: 91.5 },
      { Name: 'Seat', Confidence: 88.7 },
      { Name: 'Wood', Confidence: 85.3 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  clothing: {
    Labels: [
      { Name: 'Clothing', Confidence: 96.1 },
      { Name: 'Apparel', Confidence: 93.4 },
      { Name: 'Shirt', Confidence: 89.8 },
      { Name: 'Fashion', Confidence: 87.2 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'Nike', Confidence: 92.5, Type: 'LINE' },
      { DetectedText: 'Size M', Confidence: 88.9, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Nike',
    ConditionHint: 'Good',
  },
  book: {
    Labels: [
      { Name: 'Book', Confidence: 98.5 },
      { Name: 'Publication', Confidence: 95.7 },
      { Name: 'Text', Confidence: 92.3 },
      { Name: 'Literature', Confidence: 89.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'The Great Gatsby', Confidence: 97.2, Type: 'LINE' },
      { DetectedText: 'F. Scott Fitzgerald', Confidence: 94.8, Type: 'LINE' },
    ],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  inappropriate: {
    Labels: [
      { Name: 'Person', Confidence: 95.2 },
      { Name: 'Human', Confidence: 92.8 },
    ],
    ModerationLabels: [
      { Name: 'Suggestive', Confidence: 87.5 },
      { Name: 'Explicit Nudity', Confidence: 82.3 },
    ],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.3, Height: 0.4, Left: 0.2, Top: 0.1 },
        Confidence: 94.7,
      },
    ],
  },
  violence: {
    Labels: [
      { Name: 'Weapon', Confidence: 91.4 },
      { Name: 'Violence', Confidence: 88.7 },
    ],
    ModerationLabels: [
      { Name: 'Violence', Confidence: 91.4 },
      { Name: 'Weapons', Confidence: 88.7 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  drugs: {
    Labels: [
      { Name: 'Drug Paraphernalia', Confidence: 89.3 },
      { Name: 'Substance', Confidence: 85.6 },
    ],
    ModerationLabels: [
      { Name: 'Drugs', Confidence: 89.3 },
      { Name: 'Drug Use', Confidence: 85.6 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  withFaces: {
    Labels: [
      { Name: 'Person', Confidence: 96.8 },
      { Name: 'Human', Confidence: 94.2 },
      { Name: 'Face', Confidence: 91.5 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.25, Height: 0.35, Left: 0.3, Top: 0.2 },
        Confidence: 96.8,
      },
      {
        BoundingBox: { Width: 0.22, Height: 0.32, Left: 0.6, Top: 0.25 },
        Confidence: 94.2,
      },
    ],
  },
};

/**
 * Simulates AWS Rekognition image analysis
 */
export async function analyzeImageWithRekognition(
  imageFile: File
): Promise<RekognitionResponse> {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1500 + Math.random() * 1000)
  );

  // Determine mock response based on filename or random selection
  const fileName = imageFile.name.toLowerCase();

  if (
    fileName.includes('laptop') ||
    fileName.includes('macbook') ||
    fileName.includes('computer')
  ) {
    return MOCK_RESPONSES.laptop;
  }
  if (
    fileName.includes('phone') ||
    fileName.includes('iphone') ||
    fileName.includes('mobile')
  ) {
    return MOCK_RESPONSES.phone;
  }
  if (
    fileName.includes('chair') ||
    fileName.includes('furniture') ||
    fileName.includes('desk')
  ) {
    return MOCK_RESPONSES.furniture;
  }
  if (
    fileName.includes('shirt') ||
    fileName.includes('clothing') ||
    fileName.includes('apparel')
  ) {
    return MOCK_RESPONSES.clothing;
  }
  if (fileName.includes('book') || fileName.includes('novel')) {
    return MOCK_RESPONSES.book;
  }
  if (fileName.includes('inappropriate') || fileName.includes('nsfw')) {
    return MOCK_RESPONSES.inappropriate;
  }
  if (
    fileName.includes('face') ||
    fileName.includes('person') ||
    fileName.includes('people')
  ) {
    return MOCK_RESPONSES.withFaces;
  }

  // Random selection for generic images with edge cases
  const random = Math.random();

  // 15% chance of moderated content (various types)
  if (random < 0.15) {
    const moderatedTypes = ['inappropriate', 'violence', 'drugs'];
    const randomType =
      moderatedTypes[Math.floor(Math.random() * moderatedTypes.length)];
    return MOCK_RESPONSES[randomType as keyof typeof MOCK_RESPONSES];
  }

  // 8% chance of face detection
  if (random < 0.23) {
    return MOCK_RESPONSES.withFaces;
  }

  // 12% chance of unrecognized/poor quality image
  if (random < 0.35) {
    return {
      Labels: [
        { Name: 'Object', Confidence: 45.2 },
        { Name: 'Item', Confidence: 38.7 },
        { Name: 'Unidentified', Confidence: 32.1 },
      ],
      ModerationLabels: [],
      TextDetections: [],
      FaceDetails: [],
      ConditionHint: 'Unknown',
    };
  }

  // Regular responses for remaining 65%
  const validResponses = [
    MOCK_RESPONSES.laptop,
    MOCK_RESPONSES.phone,
    MOCK_RESPONSES.furniture,
    MOCK_RESPONSES.clothing,
    MOCK_RESPONSES.book,
  ];
  const randomIndex = Math.floor(Math.random() * validResponses.length);
  return validResponses[randomIndex];
}

/**
 * Mock AWS Bedrock service for generating natural descriptions
 */
async function generateBedrockDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 800));

  const primaryLabel = labels[0]?.toLowerCase() || 'item';
  const brandText = brand ? ` ${brand}` : '';
  const detectedText =
    textDetections.length > 0 ? textDetections.join(', ') : '';

  // Generate natural descriptions based on item type
  const descriptions: Record<string, string[]> = {
    laptop: [
      `High-performance${brandText} laptop perfect for work, gaming, and creative projects. Features modern design and reliable performance.`,
      `Sleek${brandText} laptop computer ideal for professionals and students. Excellent condition with all original features intact.`,
      `Premium${brandText} laptop offering powerful computing capabilities. Great for productivity, entertainment, and professional use.`,
    ],
    phone: [
      `Well-maintained${brandText} smartphone in excellent working condition. All features function perfectly with minimal wear.`,
      `Premium${brandText} mobile phone with advanced features and sleek design. Perfect for daily use and communication.`,
      `High-quality${brandText} smartphone offering cutting-edge technology and reliable performance for all your mobile needs.`,
    ],
    furniture: [
      `Beautiful and functional furniture piece that would make a great addition to any home. Solid construction and timeless design.`,
      `Quality furniture item in good condition. Perfect for adding style and functionality to your living space.`,
      `Well-crafted furniture piece with classic design. Ideal for both modern and traditional home decor.`,
    ],
    clothing: [
      `Stylish${brandText} apparel in great condition. Perfect for casual wear or special occasions. Comfortable fit and quality materials.`,
      `Quality${brandText} clothing item that combines style and comfort. Excellent condition with minimal signs of wear.`,
      `Fashionable${brandText} garment perfect for updating your wardrobe. Great quality and versatile styling options.`,
    ],
    book: [
      `Engaging book in excellent condition. Perfect for readers looking for quality literature and entertainment.`,
      `Well-preserved book with minimal wear. Great addition to any personal library or perfect for gifting.`,
      `Quality publication in good condition. Ideal for book lovers and collectors seeking interesting reads.`,
    ],
    default: [
      `Quality item in good condition. Well-maintained and ready for its next owner. Perfect for anyone looking for reliable functionality.`,
      `Excellent condition item that has been well cared for. Great value and ready to serve its new owner well.`,
      `Well-maintained item offering great functionality and value. Perfect condition for continued use and enjoyment.`,
    ],
  };

  const categoryDescriptions =
    descriptions[primaryLabel] || descriptions.default;
  const randomDescription =
    categoryDescriptions[
      Math.floor(Math.random() * categoryDescriptions.length)
    ];

  // Add detected text context if available
  if (detectedText) {
    return `${randomDescription} ${
      detectedText.includes('Model')
        ? `Model details: ${detectedText}.`
        : `Additional details: ${detectedText}.`
    }`;
  }

  return randomDescription;
}

/**
 * Processes Rekognition response into user-friendly format
 */
export async function processRekognitionResponse(
  response: RekognitionResponse
): Promise<ProcessedAIResult> {
  const topLabels = response.Labels.filter((label) => label.Confidence > 80)
    .slice(0, 5)
    .map((label) => label.Name);

  const textDetections = response.TextDetections.filter(
    (text) => text.Confidence > 85 && text.Type === 'LINE'
  ).map((text) => text.DetectedText);

  // Check if this is an unrecognized/poor quality image
  const isUnrecognized =
    topLabels.includes('Object') && topLabels.includes('Unidentified');

  // Generate item name from top labels and text
  let itemName = '';
  if (isUnrecognized) {
    itemName = 'Unrecognized Item';
  } else if (textDetections.length > 0) {
    itemName = textDetections[0];
  } else if (topLabels.length > 0) {
    itemName = topLabels[0];
  }

  // Generate AI-powered description using mock Bedrock
  let description = '';
  if (isUnrecognized) {
    description =
      'AI could not clearly identify this item from the image. Please manually enter the item details below for the best listing results.';
  } else {
    description = await generateBedrockDescription(
      topLabels,
      textDetections,
      response.Brand
    );
  }

  // Map labels to categories
  const category = mapLabelsToCategory(topLabels);

  // Check for content warnings
  let contentWarning: string | undefined;
  if (response.ModerationLabels.length > 0) {
    const moderationLabels = response.ModerationLabels.filter(
      (label) => label.Confidence > 75
    ).map((label) => label.Name);
    if (moderationLabels.length > 0) {
      contentWarning = `Content warning: ${moderationLabels.join(
        ', '
      )} detected`;
    }
  }

  // Check for faces
  let faceWarning: string | undefined;
  if (response.FaceDetails.length > 0) {
    faceWarning = `${response.FaceDetails.length} face(s) detected. Consider blurring faces for privacy.`;
  }

  return {
    itemName,
    description,
    category,
    condition: response.ConditionHint || 'Good',
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

/**
 * Maps detected labels to listing categories
 */
function mapLabelsToCategory(labels: string[]): string {
  const categoryMap: Record<string, string> = {
    Electronics: 'electronics',
    Laptop: 'electronics',
    Computer: 'electronics',
    'Mobile Phone': 'electronics',
    Smartphone: 'electronics',
    Furniture: 'furniture',
    Chair: 'furniture',
    Clothing: 'clothing',
    Apparel: 'clothing',
    Shirt: 'clothing',
    Book: 'books',
    Publication: 'books',
    Vehicle: 'vehicles',
    Car: 'vehicles',
    Bicycle: 'vehicles',
    'Sports Equipment': 'sports',
    Tool: 'tools',
  };

  for (const label of labels) {
    if (categoryMap[label]) {
      return categoryMap[label];
    }
  }

  return 'other';
}
