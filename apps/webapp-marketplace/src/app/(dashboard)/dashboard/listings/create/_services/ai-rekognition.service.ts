/**
 * Mock AWS Rekognition Service for AI-powered listing creation
 * This service simulates AWS Rekognition responses for image analysis
 */

export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
  ParentName?: string;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Confidence: number;
  Type: 'LINE' | 'WORD';
}

export interface RekognitionFaceDetail {
  BoundingBox: {
    Width: number;
    Height: number;
    Left: number;
    Top: number;
  };
  Confidence: number;
}

export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

/**
 * Mock data for different types of items
 */
const MOCK_RESPONSES: Record<string, RekognitionResponse> = {
  laptop: {
    Labels: [
      { Name: 'Laptop', Confidence: 95.5 },
      { Name: 'Computer', Confidence: 92.3 },
      { Name: 'Electronics', Confidence: 89.7 },
      { Name: 'Device', Confidence: 87.2 },
      { Name: 'Technology', Confidence: 85.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
      { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
      { DetectedText: '13-inch', Confidence: 91.8, Type: 'WORD' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Good',
  },
  phone: {
    Labels: [
      { Name: 'Mobile Phone', Confidence: 97.8 },
      { Name: 'Smartphone', Confidence: 95.2 },
      { Name: 'Electronics', Confidence: 93.1 },
      { Name: 'Communication Device', Confidence: 88.9 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'iPhone 14', Confidence: 96.7, Type: 'LINE' },
      { DetectedText: 'Pro Max', Confidence: 94.3, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Like New',
  },
  furniture: {
    Labels: [
      { Name: 'Furniture', Confidence: 94.2 },
      { Name: 'Chair', Confidence: 91.5 },
      { Name: 'Seat', Confidence: 88.7 },
      { Name: 'Wood', Confidence: 85.3 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  clothing: {
    Labels: [
      { Name: 'Clothing', Confidence: 96.1 },
      { Name: 'Apparel', Confidence: 93.4 },
      { Name: 'Shirt', Confidence: 89.8 },
      { Name: 'Fashion', Confidence: 87.2 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'Nike', Confidence: 92.5, Type: 'LINE' },
      { DetectedText: 'Size M', Confidence: 88.9, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Nike',
    ConditionHint: 'Good',
  },
  book: {
    Labels: [
      { Name: 'Book', Confidence: 98.5 },
      { Name: 'Publication', Confidence: 95.7 },
      { Name: 'Text', Confidence: 92.3 },
      { Name: 'Literature', Confidence: 89.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'The Great Gatsby', Confidence: 97.2, Type: 'LINE' },
      { DetectedText: 'F. Scott Fitzgerald', Confidence: 94.8, Type: 'LINE' },
    ],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  inappropriate: {
    Labels: [
      { Name: 'Person', Confidence: 95.2 },
      { Name: 'Human', Confidence: 92.8 },
    ],
    ModerationLabels: [
      { Name: 'Suggestive', Confidence: 87.5 },
      { Name: 'Explicit Nudity', Confidence: 82.3 },
    ],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.3, Height: 0.4, Left: 0.2, Top: 0.1 },
        Confidence: 94.7,
      },
    ],
  },
  withFaces: {
    Labels: [
      { Name: 'Person', Confidence: 96.8 },
      { Name: 'Human', Confidence: 94.2 },
      { Name: 'Face', Confidence: 91.5 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.25, Height: 0.35, Left: 0.3, Top: 0.2 },
        Confidence: 96.8,
      },
      {
        BoundingBox: { Width: 0.22, Height: 0.32, Left: 0.6, Top: 0.25 },
        Confidence: 94.2,
      },
    ],
  },
};

/**
 * Simulates AWS Rekognition image analysis
 */
export async function analyzeImageWithRekognition(
  imageFile: File
): Promise<RekognitionResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1500 + Math.random() * 1000));

  // Determine mock response based on filename or random selection
  const fileName = imageFile.name.toLowerCase();
  
  if (fileName.includes('laptop') || fileName.includes('macbook') || fileName.includes('computer')) {
    return MOCK_RESPONSES.laptop;
  }
  if (fileName.includes('phone') || fileName.includes('iphone') || fileName.includes('mobile')) {
    return MOCK_RESPONSES.phone;
  }
  if (fileName.includes('chair') || fileName.includes('furniture') || fileName.includes('desk')) {
    return MOCK_RESPONSES.furniture;
  }
  if (fileName.includes('shirt') || fileName.includes('clothing') || fileName.includes('apparel')) {
    return MOCK_RESPONSES.clothing;
  }
  if (fileName.includes('book') || fileName.includes('novel')) {
    return MOCK_RESPONSES.book;
  }
  if (fileName.includes('inappropriate') || fileName.includes('nsfw')) {
    return MOCK_RESPONSES.inappropriate;
  }
  if (fileName.includes('face') || fileName.includes('person') || fileName.includes('people')) {
    return MOCK_RESPONSES.withFaces;
  }

  // Random selection for generic images
  const responses = Object.values(MOCK_RESPONSES);
  const randomIndex = Math.floor(Math.random() * (responses.length - 2)); // Exclude inappropriate and withFaces
  return responses[randomIndex];
}

/**
 * Processes Rekognition response into user-friendly format
 */
export function processRekognitionResponse(response: RekognitionResponse): ProcessedAIResult {
  const topLabels = response.Labels
    .filter(label => label.Confidence > 80)
    .slice(0, 5)
    .map(label => label.Name);

  const textDetections = response.TextDetections
    .filter(text => text.Confidence > 85 && text.Type === 'LINE')
    .map(text => text.DetectedText);

  // Generate item name from top labels and text
  let itemName = '';
  if (textDetections.length > 0) {
    itemName = textDetections[0];
  } else if (topLabels.length > 0) {
    itemName = topLabels[0];
  }

  // Generate description
  const description = `${topLabels.slice(0, 3).join(', ')}${
    textDetections.length > 0 ? `. Detected text: ${textDetections.join(', ')}` : ''
  }`;

  // Map labels to categories
  const category = mapLabelsToCategory(topLabels);

  // Check for content warnings
  let contentWarning: string | undefined;
  if (response.ModerationLabels.length > 0) {
    const moderationLabels = response.ModerationLabels
      .filter(label => label.Confidence > 75)
      .map(label => label.Name);
    if (moderationLabels.length > 0) {
      contentWarning = `Content warning: ${moderationLabels.join(', ')} detected`;
    }
  }

  // Check for faces
  let faceWarning: string | undefined;
  if (response.FaceDetails.length > 0) {
    faceWarning = `${response.FaceDetails.length} face(s) detected. Consider blurring faces for privacy.`;
  }

  return {
    itemName,
    description,
    category,
    condition: response.ConditionHint || 'Good',
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

/**
 * Maps detected labels to listing categories
 */
function mapLabelsToCategory(labels: string[]): string {
  const categoryMap: Record<string, string> = {
    'Electronics': 'electronics',
    'Laptop': 'electronics',
    'Computer': 'electronics',
    'Mobile Phone': 'electronics',
    'Smartphone': 'electronics',
    'Furniture': 'furniture',
    'Chair': 'furniture',
    'Clothing': 'clothing',
    'Apparel': 'clothing',
    'Shirt': 'clothing',
    'Book': 'books',
    'Publication': 'books',
    'Vehicle': 'vehicles',
    'Car': 'vehicles',
    'Bicycle': 'vehicles',
    'Sports Equipment': 'sports',
    'Tool': 'tools',
  };

  for (const label of labels) {
    if (categoryMap[label]) {
      return categoryMap[label];
    }
  }

  return 'other';
}
