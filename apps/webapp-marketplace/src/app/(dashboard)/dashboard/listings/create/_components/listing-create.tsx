'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Brain,
  CheckCircle,
  Eye,
  Loader2,
  Upload,
  X,
} from 'lucide-react';
import { useEffect, useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';

import moment from 'moment';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

import type { Category } from '@package/db/core/models/category.model';

import {
  createListing,
  getPresignedUrls,
  listCategories,
} from '../../../_actions';
import {
  ListingCondition,
  ListingRentalUnit,
  ListingType,
} from '../../../_types/listing.types';

import {
  analyzeImageWithRekognition,
  processRekognitionResponse,
  type ProcessedAIResult,
} from '../_services/ai-rekognition.service';
import ListingGuidelines from './listing-guidelines';

import { frontendConfig } from '@package/configs';

// Form data interface for React Hook Form
interface ListingFormData {
  type: ListingType;
  name: string;
  description: string;
  categoryId: string;
  price: number;
  condition: ListingCondition;
  conditionSummary?: string;
  safetyRequirements?: string;
  rentalUnit?: ListingRentalUnit;
  startDate?: string;
  endDate?: string;
  delivery_option_pickup?: boolean;
  delivery_option_shipping?: boolean;
  delivery_option_delivery?: boolean;
}

export default function ListingCreate() {
  const { featureFlags } = frontendConfig;

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [listingType, setListingType] = useState<ListingType | null>(null);
  const [defaultStartDate, setDefaultStartDate] = useState(() => {
    // Tomorrow at noon UTC
    return moment.utc().add(1, 'day').hour(12).minute(0).second(0);
  });

  const [defaultEndDate, setDefaultEndDate] = useState(() => {
    // 1 week from tomorrow at noon UTC
    return moment.utc().add(8, 'days').hour(12).minute(0).second(0);
  });

  const [isLoading, startLoadingTransition] = useTransition();
  const [isCreatingListing, setCreatingListingTransition] = useTransition();

  const [categories, setCategories] = useState<Category[]>([]);

  // photo upload
  const [photos, setPhotos] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // AI functionality state
  const [useAI, setUseAI] = useState(true);
  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);
  const [aiResult, setAiResult] = useState<ProcessedAIResult | null>(null);
  const [aiPhotoFile, setAiPhotoFile] = useState<File | null>(null);

  // React Hook Form setup with validation
  const form = useForm<ListingFormData>({
    defaultValues: {
      type: ListingType.SELL,
      name: '',
      description: '',
      categoryId: '',
      price: 0,
      condition: ListingCondition.GOOD,
      conditionSummary: '',
      safetyRequirements: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    startLoadingTransition(async () => {
      const categoriesList = await listCategories();
      setCategories(categoriesList);
    });
  }, []);

  // AI photo upload and analysis handler
  const handleAIPhotoUpload = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const file = e.target.files[0];
    const validation = validateFile(file);

    if (!validation.valid) {
      setErrorMessage(validation.error || 'Invalid file');
      return;
    }

    // Clear previous results and form fields on new upload
    setAiResult(null);
    form.reset({
      type: form.getValues('type'), // Keep the listing type
      name: '',
      description: '',
      categoryId: '',
      price: 0,
      condition: ListingCondition.GOOD,
      conditionSummary: '',
      safetyRequirements: '',
    });

    setAiPhotoFile(file);
    setIsAnalyzingImage(true);
    setErrorMessage(null);

    try {
      const rekognitionResponse = await analyzeImageWithRekognition(file);
      const processedResult = await processRekognitionResponse(
        rekognitionResponse
      );
      setAiResult(processedResult);

      // Don't auto-fill if content is flagged for moderation
      if (!processedResult.contentWarning) {
        // Auto-fill form fields only for clean content
        form.setValue('name', processedResult.itemName);
        form.setValue('description', processedResult.description);
        form.setValue(
          'condition',
          processedResult.condition as ListingCondition
        );

        // Find matching category (only if not zero analysis)
        if (processedResult.itemName !== 'No Data Detected') {
          const matchingCategory = categories.find(
            (cat) =>
              cat.name
                .toLowerCase()
                .includes(processedResult.category.toLowerCase()) ||
              processedResult.category
                .toLowerCase()
                .includes(cat.name.toLowerCase())
          );
          if (matchingCategory) {
            form.setValue('categoryId', matchingCategory.categoryId);
          }
        }

        // Add the photo to the photos array (only if content is clean)
        setPhotos((prev) => {
          const nonAIPhotos = prev.filter(
            (_, index) => index !== 0 || !aiPhotoFile
          );
          return [file, ...nonAIPhotos].slice(0, 10);
        });
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      setErrorMessage('Failed to analyze image. Please try again.');
    } finally {
      setIsAnalyzingImage(false);
    }
  };

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  const formatDateForDatetimeLocal = (momentDate: moment.Moment): string => {
    return momentDate.format('YYYY-MM-DDTHH:mm');
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not supported. Please use JPG, PNG, GIF or WebP.`,
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File "${file.name}" is too large (${(
          file.size /
          1024 /
          1024
        ).toFixed(1)}MB). Maximum size is 10MB.`,
      };
    }

    return { valid: true };
  };

  const handlePhotoDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files);

    // Validate files
    const validFiles: File[] = [];
    const errors: string[] = [];

    for (const file of droppedFiles) {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else if (validation.error) {
        errors.push(validation.error);
      }
    }

    if (errors.length > 0) {
      setErrorMessage(`Some files were not added: ${errors.join('. ')}`);
    }

    setPhotos((prev) => [...prev, ...validFiles].slice(0, 10));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const uploadedFiles = Array.from(e.target.files);

      // Validate files
      const validFiles: File[] = [];
      const errors: string[] = [];

      for (const file of uploadedFiles) {
        const validation = validateFile(file);
        if (validation.valid) {
          validFiles.push(file);
        } else if (validation.error) {
          errors.push(validation.error);
        }
      }

      if (errors.length > 0) {
        setErrorMessage(`Some files were not added: ${errors.join('. ')}`);
      }

      setPhotos((prev) => [...prev, ...validFiles].slice(0, 10));
    }
  };

  const removePhoto = (index: number) => {
    setPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const advancedUploadPhotos = async (): Promise<string[]> => {
    if (photos.length === 0) return [];

    setIsUploading(true);
    const urls: string[] = [];

    try {
      // Step 1: Get presigned URLs for each file
      const fileTypes = photos.map((photo) => photo.type);
      const {
        urls: presignedUrls,
        bucket,
        region,
        error,
      } = await getPresignedUrls(fileTypes);

      if (error || !presignedUrls) {
        throw new Error(error || 'Failed to get upload URLs');
      }

      // Step 2: Upload files directly to S3 using the presigned URLs
      for (let i = 0; i < photos.length; i++) {
        const photo = photos[i];
        const { url: presignedUrl, key } = presignedUrls[i];

        // Upload directly to S3 using fetch
        const uploadResponse = await fetch(presignedUrl, {
          method: 'PUT',
          body: photo,
          headers: {
            'Content-Type': photo.type,
          },
        });

        if (!uploadResponse.ok) {
          throw new Error(
            `Failed to upload image ${i + 1}: ${uploadResponse.statusText}`
          );
        }

        // Generate the final URL using the bucket and region from the server
        // TODO: set the path in package/configs
        const fileUrl = `https://${bucket}.s3.${region}.amazonaws.com/${key}`;

        urls.push(fileUrl);
        setUploadProgress(Math.round(((i + 1) / photos.length) * 100));
      }

      setUploadedUrls(urls);
      return urls;
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const advancedHandleSubmit = async (data: ListingFormData) => {
    setErrorMessage(null);
    setSuccessMessage(null);

    // Block submission if content is flagged
    if (aiResult?.contentWarning) {
      setErrorMessage(
        'Cannot create listing with flagged content. Please upload a different photo.'
      );
      return;
    }

    setCreatingListingTransition(async () => {
      try {
        // First upload the photos
        let imageUrls: string[] = [];

        if (photos.length > 0) {
          try {
            // Use the advanced upload method that uses presigned URLs
            imageUrls = await advancedUploadPhotos();
          } catch (error) {
            setErrorMessage('Failed to upload photos. Please try again.');
            return;
          }
        }

        // Create a new FormData object from the form data
        const formData = new FormData();

        // Add form fields
        formData.append('type', data.type);
        formData.append('name', data.name);
        formData.append('description', data.description);
        formData.append('categoryId', data.categoryId);
        formData.append('price', data.price.toString());
        formData.append('condition', data.condition);

        if (data.conditionSummary) {
          formData.append('conditionSummary', data.conditionSummary);
        }
        if (data.safetyRequirements) {
          formData.append('safetyRequirements', data.safetyRequirements);
        }

        // Add delivery options
        if (data.delivery_option_pickup) {
          formData.append('delivery_option_pickup', 'on');
        }
        if (data.delivery_option_shipping) {
          formData.append('delivery_option_shipping', 'on');
        }
        if (data.delivery_option_delivery) {
          formData.append('delivery_option_delivery', 'on');
        }

        // Add the uploaded image URLs to the form data
        if (imageUrls.length > 0) {
          formData.append(
            'imageUrls',
            JSON.stringify(
              imageUrls.map((url, index) => ({
                url,
                isMain: index === 0, // First image is the main image
              }))
            )
          );
        }

        // Add rental availability data if listing type is RENT
        if (data.type === ListingType.RENT) {
          const startDate = data.startDate;
          const endDate = data.endDate;

          if (startDate && endDate) {
            // NOTE: single availability period for now
            const rentalAvailability = [
              {
                startDate: moment.utc(startDate).format(),
                endDate: moment.utc(endDate).format(),
              },
            ];

            if (data.rentalUnit) {
              formData.append('rentalUnit', data.rentalUnit);
            }
            formData.append(
              'rentalAvailability',
              JSON.stringify(rentalAvailability)
            );
          }
        }

        const result = await createListing(formData);

        if (!result) {
          setErrorMessage(
            'There was an error creating the listing. Please try again.'
          );
          return;
        }

        setSuccessMessage('Listing created successfully');
        setPhotos([]); // Clear photos after successful creation
        setAiResult(null); // Clear AI result
        setAiPhotoFile(null); // Clear AI photo
        form.reset(); // Reset form fields
      } catch (e) {
        setErrorMessage(
          'An error occurred while creating the listing. Please try again.'
        );
      }
    });
  };

  // Calculate current step for progress indicator
  const getCurrentStep = () => {
    if (!useAI) return 2; // Skip to manual entry
    if (!aiResult) return 1; // AI analysis step
    return 2; // Form completion step
  };

  return (
    <div className="container p-6 mx-auto">
      <div className="mb-8">
        <h1 className="mb-4 text-3xl font-bold">Create a Listing</h1>

        {/* Progress Steps */}
        <div className="flex items-center mb-6 space-x-4">
          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 1 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 1 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              1
            </div>
            <span className="font-medium">
              {useAI ? 'AI Analysis' : 'Setup'}
            </span>
          </div>

          <div
            className={`h-px flex-1 ${
              getCurrentStep() >= 2 ? 'bg-primary' : 'bg-gray-200'
            }`}
          ></div>

          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 2 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 2 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              2
            </div>
            <span className="font-medium">Complete Details</span>
          </div>

          <div
            className={`h-px flex-1 ${
              getCurrentStep() >= 3 ? 'bg-primary' : 'bg-gray-200'
            }`}
          ></div>

          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 3 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 3 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              3
            </div>
            <span className="font-medium">Publish</span>
          </div>
        </div>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(advancedHandleSubmit)}>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-6">
              {/* AI Toggle */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    AI-Powered Listing
                  </CardTitle>
                  <CardDescription>
                    Use AI to automatically fill listing details from a photo
                  </CardDescription>
                  <div className="p-3 mt-3 border rounded-lg bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800">
                    <p className="text-xs text-amber-700 dark:text-amber-300">
                      <strong>⚠️ AI Disclaimer:</strong> AI analysis is not
                      perfect and may make mistakes. Always review and verify
                      the auto-filled information before publishing your
                      listing. Some images may not be recognized or may be
                      flagged for content review.
                    </p>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="use-ai"
                        checked={useAI}
                        onCheckedChange={setUseAI}
                      />
                      <Label htmlFor="use-ai">
                        Use AI to fill details from photo
                      </Label>
                    </div>

                    {!useAI && (
                      <div className="p-4 border rounded-lg bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900 dark:to-slate-900">
                        <div className="flex items-start space-x-3">
                          <div className="w-5 h-5 rounded-full bg-gray-400 flex items-center justify-center mt-0.5">
                            <span className="text-xs font-bold text-white">
                              !
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-700 dark:text-gray-300">
                              Manual Entry Mode
                            </p>
                            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                              Fill in all the listing details manually below.
                              You can enable AI anytime to auto-fill from a
                              photo.
                            </p>
                            <div className="mt-2">
                              <button
                                type="button"
                                onClick={() => setUseAI(true)}
                                className="text-xs font-medium text-primary hover:text-primary/80"
                              >
                                Enable AI Analysis →
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* AI Photo Upload */}
              {useAI && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Upload className="w-5 h-5" />
                      Upload Photo for AI Analysis
                    </CardTitle>
                    <CardDescription>
                      Upload a photo and AI will analyze it to fill in the
                      listing details
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div
                        className="p-6 text-center transition-colors border-2 border-gray-300 border-dashed rounded-lg cursor-pointer hover:border-primary/50"
                        onClick={() =>
                          document.getElementById('ai-photo-upload')?.click()
                        }
                      >
                        {isAnalyzingImage ? (
                          <div className="flex flex-col items-center space-y-3">
                            <Loader2 className="w-12 h-12 mx-auto text-primary animate-spin" />
                            <div className="text-center">
                              <p className="font-medium">
                                Analyzing image with AI...
                              </p>
                              <p className="mt-1 text-sm text-gray-500">
                                This may take a few seconds
                              </p>
                            </div>
                            <div className="w-full h-2 max-w-xs bg-gray-200 rounded-full">
                              <div
                                className="h-2 rounded-full bg-primary animate-pulse"
                                style={{ width: '60%' }}
                              ></div>
                            </div>
                          </div>
                        ) : aiResult ? (
                          <div className="flex flex-col items-center space-y-2">
                            <CheckCircle className="w-12 h-12 mx-auto text-green-500" />
                            <p className="font-medium text-green-700">
                              Analysis Complete!
                            </p>
                            <p className="text-sm text-center text-gray-600">
                              Upload a new photo to analyze again
                            </p>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center space-y-2">
                            <Brain className="w-12 h-12 mx-auto text-gray-400" />
                            <p className="font-medium">
                              Upload Photo for AI Analysis
                            </p>
                            <p className="text-sm text-center text-gray-500">
                              AI will automatically fill in listing details
                            </p>
                          </div>
                        )}
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleAIPhotoUpload}
                          id="ai-photo-upload"
                          disabled={isAnalyzingImage}
                        />
                      </div>

                      {/* AI Results Display */}
                      {aiResult && (
                        <div className="p-4 space-y-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                          <h4 className="flex items-center gap-2 font-semibold text-blue-900 dark:text-blue-100">
                            <Eye className="w-4 h-4" />
                            AI Analysis Results
                          </h4>

                          {aiResult.detectedLabels.length > 0 && (
                            <div>
                              <p className="mb-2 text-sm font-medium text-blue-800 dark:text-blue-200">
                                Detected Labels:
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {aiResult.detectedLabels.map((label, index) => (
                                  <Badge
                                    key={index}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {label}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {aiResult.detectedBrand && (
                            <div>
                              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                Detected Brand:
                              </p>
                              <Badge variant="outline">
                                {aiResult.detectedBrand}
                              </Badge>
                            </div>
                          )}

                          {aiResult.textDetections.length > 0 && (
                            <div>
                              <p className="mb-2 text-sm font-medium text-blue-800 dark:text-blue-200">
                                Text Found:
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {aiResult.textDetections.map((text, index) => (
                                  <Badge
                                    key={index}
                                    variant="outline"
                                    className="text-xs"
                                  >
                                    {text}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}

                          {aiResult.contentWarning && (
                            <Alert variant="destructive">
                              <AlertCircle className="w-4 h-4" />
                              <AlertTitle>
                                🚫 Content Policy Violation
                              </AlertTitle>
                              <AlertDescription>
                                {aiResult.contentWarning}
                                <div className="p-3 mt-3 bg-red-100 border border-red-200 rounded dark:bg-red-900/30 dark:border-red-800">
                                  <p className="font-semibold text-red-800 dark:text-red-200">
                                    ⛔ LISTING BLOCKED
                                  </p>
                                  <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                                    This image violates our community guidelines
                                    and cannot be used for creating a listing.
                                    Please upload a different photo that
                                    complies with our policies to continue.
                                  </p>
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      onClick={() => {
                                        setAiResult(null);
                                        setAiPhotoFile(null);
                                        document
                                          .getElementById('ai-photo-upload')
                                          ?.click();
                                      }}
                                      className="px-3 py-1 text-sm text-white bg-red-600 rounded hover:bg-red-700"
                                    >
                                      Upload Different Photo
                                    </button>
                                  </div>
                                </div>
                              </AlertDescription>
                            </Alert>
                          )}

                          {aiResult.faceWarning && (
                            <Alert>
                              <Eye className="w-4 h-4" />
                              <AlertTitle>Privacy Notice</AlertTitle>
                              <AlertDescription>
                                {aiResult.faceWarning}
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>
                      )}

                      {/* Success message after AI analysis */}
                      {aiResult && !aiResult.contentWarning && (
                        <div
                          className={`p-4 border rounded-lg ${
                            aiResult.itemName === 'No Data Detected'
                              ? 'bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-red-200 dark:border-red-800'
                              : aiResult.itemName === 'Unrecognized Item'
                              ? 'bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border-orange-200 dark:border-orange-800'
                              : 'bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-green-200 dark:border-green-800'
                          }`}
                        >
                          <div className="flex items-start space-x-3">
                            {aiResult.itemName === 'No Data Detected' ? (
                              <X className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                            ) : aiResult.itemName === 'Unrecognized Item' ? (
                              <AlertCircle className="w-5 h-5 text-orange-500 mt-0.5 flex-shrink-0" />
                            ) : (
                              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                            )}
                            <div>
                              <p
                                className={`font-medium ${
                                  aiResult.itemName === 'No Data Detected'
                                    ? 'text-red-700 dark:text-red-300'
                                    : aiResult.itemName === 'Unrecognized Item'
                                    ? 'text-orange-700 dark:text-orange-300'
                                    : 'text-green-700 dark:text-green-300'
                                }`}
                              >
                                {aiResult.itemName === 'No Data Detected'
                                  ? 'No Data Detected in Image'
                                  : aiResult.itemName === 'Unrecognized Item'
                                  ? 'AI Could Not Identify Item'
                                  : 'AI Analysis Complete!'}
                              </p>
                              <p
                                className={`mt-1 text-sm ${
                                  aiResult.itemName === 'No Data Detected'
                                    ? 'text-red-600 dark:text-red-400'
                                    : aiResult.itemName === 'Unrecognized Item'
                                    ? 'text-orange-600 dark:text-orange-400'
                                    : 'text-green-600 dark:text-green-400'
                                }`}
                              >
                                {aiResult.itemName === 'No Data Detected'
                                  ? 'The image appears to be empty, too dark, blurry, or unclear. Please upload a clearer photo or proceed with manual entry.'
                                  : aiResult.itemName === 'Unrecognized Item'
                                  ? 'The image quality may be poor or the item is unclear. Please manually fill in the details below for the best results.'
                                  : 'Form fields have been auto-filled below. Review and edit them before publishing your listing.'}
                              </p>
                              {aiResult.itemName !== 'Unrecognized Item' &&
                                aiResult.itemName !== 'No Data Detected' && (
                                  <div className="flex items-center mt-2 space-x-4 text-xs text-green-600 dark:text-green-400">
                                    <span>✓ Item identified</span>
                                    <span>✓ Description generated</span>
                                    <span>✓ Category suggested</span>
                                    <span>✓ Condition estimated</span>
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Helpful tips for problematic images */}
                      {aiResult &&
                        (aiResult.contentWarning ||
                          aiResult.itemName === 'Unrecognized Item' ||
                          aiResult.itemName === 'No Data Detected') && (
                          <div className="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                            <h5 className="mb-2 font-medium text-blue-900 dark:text-blue-100">
                              💡 Tips for Better AI Results
                            </h5>
                            <ul className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                              <li>
                                • Use well-lit, clear photos with good focus
                              </li>
                              <li>• Ensure the item fills most of the frame</li>
                              <li>• Avoid cluttered backgrounds</li>
                              <li>
                                • Take photos from multiple angles if needed
                              </li>
                              <li>
                                • Ensure content complies with community
                                guidelines
                              </li>
                            </ul>
                          </div>
                        )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Item Details - Show only when AI is off OR AI analysis is complete */}
              {(!useAI || aiResult) && (
                <Card>
                  <CardHeader>
                    <CardTitle>Item Details</CardTitle>
                    {useAI && aiResult && (
                      <CardDescription>
                        Fields have been auto-filled by AI. You can edit them
                        below.
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Listing Type</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              field.onChange(value);
                              setListingType(value as ListingType);
                            }}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select listing type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {Object.entries(ListingType).map(
                                ([key, value]) => (
                                  <SelectItem key={key} value={value}>
                                    {key.charAt(0).toUpperCase() +
                                      key
                                        .slice(1)
                                        .toLowerCase()
                                        .replace('_', ' ')}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="name"
                      rules={{
                        required: 'Item name is required',
                        minLength: {
                          value: 3,
                          message: 'Item name must be at least 3 characters',
                        },
                        maxLength: {
                          value: 100,
                          message: 'Item name must be less than 100 characters',
                        },
                      }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Item name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={
                                listingType === ListingType.SWAP
                                  ? 'Eg: Swap Camping stove for hiking backpack'
                                  : 'Describe your item'
                              }
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="categoryId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={
                                    isLoading
                                      ? 'Loading categories'
                                      : 'Select category'
                                  }
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem
                                  key={category.categoryId}
                                  value={category.categoryId}
                                >
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {listingType !== ListingType.FREE &&
                      listingType !== ListingType.SWAP && (
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Price</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="Enter price"
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(Number(e.target.value))
                                  }
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    {featureFlags.marketplace.enableDeliveryOptions && (
                      <div>
                        <Label>Delivery Options</Label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="pickup"
                              name="delivery_option_pickup"
                            />
                            <Label htmlFor="pickup">Local Pickup</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="shipping"
                              name="delivery_option_shipping"
                            />
                            <Label htmlFor="shipping">Shipping</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="delivery"
                              name="delivery_option_delivery"
                            />
                            <Label htmlFor="delivery">Local Delivery</Label>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Add Rental  */}
              {listingType === ListingType.RENT && (
                <Card>
                  <CardHeader>
                    <CardTitle>Rental Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="rentalUnit">Rental Unit</Label>
                      <Select
                        name="rentalUnit"
                        required={listingType === ListingType.RENT}
                      >
                        <SelectTrigger id="rentalUnit">
                          <SelectValue placeholder="Select rental unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(ListingRentalUnit).map(
                            ([key, value]) => (
                              <SelectItem key={key} value={value}>
                                {key.charAt(0).toUpperCase() +
                                  key.slice(1).toLowerCase()}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4">
                      <Label>Rental Availability</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="startDate">Start Date</Label>
                          <Input
                            type="datetime-local"
                            id="startDate"
                            name="startDate"
                            min={formatDateForDatetimeLocal(
                              moment.utc().add(1, 'day')
                            )}
                            defaultValue={formatDateForDatetimeLocal(
                              defaultStartDate
                            )}
                            required={listingType === ListingType.RENT}
                            onChange={(e) => {
                              const newStartDate = moment.utc(e.target.value);
                              if (
                                newStartDate.isValid() &&
                                newStartDate.isAfter(defaultEndDate)
                              ) {
                                const newEndDate = moment
                                  .utc(newStartDate)
                                  .add(7, 'days');
                                setDefaultEndDate(newEndDate);
                                const endDateInput = document.getElementById(
                                  'endDate'
                                ) as HTMLInputElement;
                                if (endDateInput) {
                                  endDateInput.value =
                                    formatDateForDatetimeLocal(newEndDate);
                                }
                              }
                            }}
                          />
                        </div>
                        <div>
                          <Label htmlFor="endDate">End Date</Label>
                          <Input
                            type="datetime-local"
                            id="endDate"
                            name="endDate"
                            min={formatDateForDatetimeLocal(defaultStartDate)}
                            defaultValue={formatDateForDatetimeLocal(
                              defaultEndDate
                            )}
                            required={listingType === ListingType.RENT}
                          />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        This defines when your item is available for rent. You
                        can add more availability periods later. Rental start
                        date must be at least tomorrow.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Item Condition - Show only when AI is off OR AI analysis is complete */}
              {(!useAI || aiResult) && (
                <Card>
                  <CardHeader>
                    <CardTitle>Item Condition</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="condition"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>Condition</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col space-y-1"
                            >
                              {Object.entries(ListingCondition).map(
                                ([key, value]) => (
                                  <FormItem
                                    key={key}
                                    className="flex items-center space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <RadioGroupItem value={value} />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      {key.charAt(0).toUpperCase() +
                                        key
                                          .slice(1)
                                          .toLowerCase()
                                          .replace('_', ' ')}
                                    </FormLabel>
                                  </FormItem>
                                )
                              )}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {featureFlags.marketplace.enableItemConditionSummary && (
                      <div>
                        <Label htmlFor="conditionSummary">
                          Condition Summary
                        </Label>
                        <Textarea
                          id="conditionSummary"
                          name="conditionSummary"
                          placeholder="Describe the condition"
                        />
                      </div>
                    )}

                    {featureFlags.marketplace
                      .enableItemConditionSafetyRequirements && (
                      <div>
                        <Label htmlFor="safetyRequirements">
                          Safety Requirements
                        </Label>
                        <Textarea
                          id="safetyRequirements"
                          name="safetyRequirements"
                          placeholder="List any safety requirements"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Upload Photos - Show only when AI is off OR when user wants to add more photos */}
              {(!useAI || aiResult) && (
                <Card>
                  <CardHeader>
                    <CardTitle>Upload Photos</CardTitle>
                    <CardDescription>
                      {useAI && aiResult
                        ? 'Add more photos to your listing'
                        : 'Drag and drop up to 10 photos'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
                    <div
                      className="p-6 text-center border-2 border-gray-300 border-dashed rounded-lg cursor-pointer"
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={handlePhotoDrop}
                      onClick={() =>
                        document.getElementById('photo-upload')?.click()
                      }
                    >
                      <Upload className="w-12 h-12 mx-auto text-gray-400" />
                      <p className="mt-1">
                        Drag and drop your photos here, or click to select files
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        className="hidden"
                        onChange={handlePhotoUpload}
                        id="photo-upload"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className="mt-2"
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent triggering the parent div's onClick
                          document.getElementById('photo-upload')?.click();
                        }}
                      >
                        Select Files
                      </Button>
                    </div>

                    <div className="mt-4">
                      {isUploading && (
                        <div className="mb-4">
                          <p className="mb-2">
                            Uploading photos: {uploadProgress}%
                          </p>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="bg-primary h-2.5 rounded-full"
                              style={{ width: `${uploadProgress}%` }}
                            />
                          </div>
                        </div>
                      )}

                      <div className="grid grid-cols-5 gap-4">
                        {photos.map((photo, index) => (
                          <div
                            key={`${photo.name}-${photo.lastModified}`}
                            className="relative"
                          >
                            <img
                              src={URL.createObjectURL(photo)}
                              alt={`Item preview ${index + 1}`}
                              className="object-cover w-full h-20 rounded"
                            />
                            <button
                              type="button"
                              onClick={() => removePhoto(index)}
                              className="absolute top-0 right-0 p-1 text-white bg-red-500 rounded-full"
                              disabled={isUploading}
                            >
                              <X className="w-4 h-4" />
                            </button>
                            {index === 0 && (
                              <span className="absolute bottom-0 left-0 px-2 py-1 text-xs text-white rounded-tr-lg rounded-bl-lg bg-primary">
                                Main
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-6">
                      <Button type="submit" disabled={isCreatingListing}>
                        {isCreatingListing ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />{' '}
                            Creating...
                          </>
                        ) : (
                          'Create Listing'
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
            {/* ListingGuidelines on the right for desktop, bottom for mobile/tablet */}
            <div className="hidden md:block">
              <ListingGuidelines />
            </div>
          </div>
          {/* Show ListingGuidelines at the bottom for mobile/tablet */}
          <div className="block mt-8 md:hidden">
            <ListingGuidelines />
          </div>
          {errorMessage && (
            <Alert
              variant="destructive"
              className="mt-6 duration-300 border-red-500 bg-red-50 dark:bg-red-900/20 animate-in fade-in slide-in-from-top-5"
            >
              <AlertCircle className="w-5 h-5 text-red-500" />
              <AlertTitle className="font-semibold text-red-700 dark:text-red-300">
                Error
              </AlertTitle>
              <AlertDescription className="text-red-600 dark:text-red-200">
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}
          {successMessage && (
            <Alert className="mt-6 duration-300 border-green-500 bg-green-50 dark:bg-green-900/20 animate-in fade-in slide-in-from-top-5">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <AlertTitle className="font-semibold text-green-700 dark:text-green-300">
                Success
              </AlertTitle>
              <AlertDescription className="text-green-600 dark:text-green-200">
                {successMessage}
              </AlertDescription>
            </Alert>
          )}
        </form>
      </Form>
    </div>
  );
}
