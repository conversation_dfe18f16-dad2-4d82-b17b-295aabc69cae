'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Loader2 } from 'lucide-react';
import { useEffect, useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';

import moment from 'moment';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import type { Category } from '@package/db/core/models/category.model';

import {
  createListing,
  getPresignedUrls,
  listCategories,
} from '../../../_actions';
import {
  ListingCondition,
  ListingRentalUnit,
  ListingType,
} from '../../../_types/listing.types';

import type { ProcessedAIResult } from '../_services/ai-rekognition.service';
import AIAnalysis from './ai-analysis';
import ListingFormFields from './listing-form-fields';
import ListingGuidelines from './listing-guidelines';
import PhotoUpload from './photo-upload';

import { frontendConfig } from '@package/configs';

// Form data interface for React Hook Form
interface ListingFormData {
  type: ListingType;
  name: string;
  description: string;
  categoryId: string;
  price: number;
  condition: ListingCondition;
  conditionSummary?: string;
  safetyRequirements?: string;
  rentalUnit?: ListingRentalUnit;
  startDate?: string;
  endDate?: string;
  delivery_option_pickup?: boolean;
  delivery_option_shipping?: boolean;
  delivery_option_delivery?: boolean;
}

export default function ListingCreate() {
  const { featureFlags } = frontendConfig;

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [listingType, setListingType] = useState<ListingType | null>(null);
  const [defaultStartDate] = useState(() => {
    // Tomorrow at noon UTC
    return moment.utc().add(1, 'day').hour(12).minute(0).second(0);
  });

  const [defaultEndDate, setDefaultEndDate] = useState(() => {
    // 1 week from tomorrow at noon UTC
    return moment.utc().add(8, 'days').hour(12).minute(0).second(0);
  });

  const [isLoading, startLoadingTransition] = useTransition();
  const [isCreatingListing, setCreatingListingTransition] = useTransition();

  const [categories, setCategories] = useState<Category[]>([]);

  // photo upload
  const [photos, setPhotos] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // AI functionality state
  const [useAI, setUseAI] = useState(true);
  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);
  const [aiResult, setAiResult] = useState<ProcessedAIResult | null>(null);
  const [aiPhotoFile, setAiPhotoFile] = useState<File | null>(null);

  // React Hook Form setup with validation
  const form = useForm<ListingFormData>({
    defaultValues: {
      type: ListingType.SELL,
      name: '',
      description: '',
      categoryId: '',
      price: 0,
      condition: ListingCondition.GOOD,
      conditionSummary: '',
      safetyRequirements: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    startLoadingTransition(async () => {
      const categoriesList = await listCategories();
      setCategories(categoriesList);
    });
  }, []);

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  const formatDateForDatetimeLocal = (momentDate: moment.Moment): string => {
    return momentDate.format('YYYY-MM-DDTHH:mm');
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `File type "${file.type}" is not supported. Please use JPG, PNG, GIF or WebP.`,
      };
    }

    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File "${file.name}" is too large (${(
          file.size /
          1024 /
          1024
        ).toFixed(1)}MB). Maximum size is 10MB.`,
      };
    }

    return { valid: true };
  };

  const advancedUploadPhotos = async (): Promise<string[]> => {
    if (photos.length === 0) return [];

    setIsUploading(true);
    const urls: string[] = [];

    try {
      // Step 1: Get presigned URLs for each file
      const fileTypes = photos.map((photo) => photo.type);
      const {
        urls: presignedUrls,
        bucket,
        region,
        error,
      } = await getPresignedUrls(fileTypes);

      if (error || !presignedUrls) {
        throw new Error(error || 'Failed to get upload URLs');
      }

      // Step 2: Upload files directly to S3 using the presigned URLs
      for (let i = 0; i < photos.length; i++) {
        const photo = photos[i];
        const { url: presignedUrl, key } = presignedUrls[i];

        // Upload directly to S3 using fetch
        const uploadResponse = await fetch(presignedUrl, {
          method: 'PUT',
          body: photo,
          headers: {
            'Content-Type': photo.type,
          },
        });

        if (!uploadResponse.ok) {
          throw new Error(
            `Failed to upload image ${i + 1}: ${uploadResponse.statusText}`
          );
        }

        // Generate the final URL using the bucket and region from the server
        // TODO: set the path in package/configs
        const fileUrl = `https://${bucket}.s3.${region}.amazonaws.com/${key}`;

        urls.push(fileUrl);
        setUploadProgress(Math.round(((i + 1) / photos.length) * 100));
      }

      setUploadedUrls(urls);
      return urls;
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const advancedHandleSubmit = async (data: ListingFormData) => {
    setErrorMessage(null);
    setSuccessMessage(null);

    // Block submission if content is flagged
    if (aiResult?.contentWarning) {
      setErrorMessage(
        'Cannot create listing with flagged content. Please upload a different photo.'
      );
      return;
    }

    setCreatingListingTransition(async () => {
      try {
        // First upload the photos
        let imageUrls: string[] = [];

        if (photos.length > 0) {
          try {
            // Use the advanced upload method that uses presigned URLs
            imageUrls = await advancedUploadPhotos();
          } catch (error) {
            setErrorMessage('Failed to upload photos. Please try again.');
            return;
          }
        }

        // Create a new FormData object from the form data
        const formData = new FormData();

        // Add form fields
        formData.append('type', data.type);
        formData.append('name', data.name);
        formData.append('description', data.description);
        formData.append('categoryId', data.categoryId);
        formData.append('price', data.price.toString());
        formData.append('condition', data.condition);

        if (data.conditionSummary) {
          formData.append('conditionSummary', data.conditionSummary);
        }
        if (data.safetyRequirements) {
          formData.append('safetyRequirements', data.safetyRequirements);
        }

        // Add delivery options
        if (data.delivery_option_pickup) {
          formData.append('delivery_option_pickup', 'on');
        }
        if (data.delivery_option_shipping) {
          formData.append('delivery_option_shipping', 'on');
        }
        if (data.delivery_option_delivery) {
          formData.append('delivery_option_delivery', 'on');
        }

        // Add the uploaded image URLs to the form data
        if (imageUrls.length > 0) {
          formData.append(
            'imageUrls',
            JSON.stringify(
              imageUrls.map((url, index) => ({
                url,
                isMain: index === 0, // First image is the main image
              }))
            )
          );
        }

        // Add rental availability data if listing type is RENT
        if (data.type === ListingType.RENT) {
          const startDate = data.startDate;
          const endDate = data.endDate;

          if (startDate && endDate) {
            // NOTE: single availability period for now
            const rentalAvailability = [
              {
                startDate: moment.utc(startDate).format(),
                endDate: moment.utc(endDate).format(),
              },
            ];

            if (data.rentalUnit) {
              formData.append('rentalUnit', data.rentalUnit);
            }
            formData.append(
              'rentalAvailability',
              JSON.stringify(rentalAvailability)
            );
          }
        }

        const result = await createListing(formData);

        if (!result) {
          setErrorMessage(
            'There was an error creating the listing. Please try again.'
          );
          return;
        }

        setSuccessMessage('Listing created successfully');
        setPhotos([]); // Clear photos after successful creation
        setAiResult(null); // Clear AI result
        setAiPhotoFile(null); // Clear AI photo
        form.reset(); // Reset form fields
      } catch (e) {
        setErrorMessage(
          'An error occurred while creating the listing. Please try again.'
        );
      }
    });
  };

  // Calculate current step for progress indicator
  const getCurrentStep = () => {
    if (!useAI) return 2; // Skip to manual entry
    if (!aiResult) return 1; // AI analysis step
    return 2; // Form completion step
  };

  return (
    <div className="container p-6 mx-auto">
      <div className="mb-8">
        <h1 className="mb-4 text-3xl font-bold">Create a Listing</h1>

        {/* Progress Steps */}
        <div className="flex items-center mb-6 space-x-4">
          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 1 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 1 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              1
            </div>
            <span className="font-medium">
              {useAI ? 'AI Analysis' : 'Setup'}
            </span>
          </div>

          <div
            className={`h-px flex-1 ${
              getCurrentStep() >= 2 ? 'bg-primary' : 'bg-gray-200'
            }`}
          ></div>

          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 2 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 2 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              2
            </div>
            <span className="font-medium">Complete Details</span>
          </div>

          <div
            className={`h-px flex-1 ${
              getCurrentStep() >= 3 ? 'bg-primary' : 'bg-gray-200'
            }`}
          ></div>

          <div
            className={`flex items-center space-x-2 ${
              getCurrentStep() >= 3 ? 'text-primary' : 'text-gray-400'
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                getCurrentStep() >= 3 ? 'bg-primary text-white' : 'bg-gray-200'
              }`}
            >
              3
            </div>
            <span className="font-medium">Publish</span>
          </div>
        </div>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(advancedHandleSubmit)}>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="space-y-6">
              <AIAnalysis
                useAI={useAI}
                setUseAI={setUseAI}
                aiResult={aiResult}
                setAiResult={setAiResult}
                isAnalyzingImage={isAnalyzingImage}
                setIsAnalyzingImage={setIsAnalyzingImage}
                aiPhotoFile={aiPhotoFile}
                setAiPhotoFile={setAiPhotoFile}
                form={form}
                categories={categories}
                photos={photos}
                setPhotos={setPhotos}
                setErrorMessage={setErrorMessage}
                validateFile={validateFile}
              />

              <ListingFormFields
                form={form}
                categories={categories}
                isLoading={isLoading}
                listingType={listingType}
                setListingType={setListingType}
                useAI={useAI}
                aiResult={aiResult}
              />

              {/* Add Rental  */}
              {listingType === ListingType.RENT && (
                <Card>
                  <CardHeader>
                    <CardTitle>Rental Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="rentalUnit">Rental Unit</Label>
                      <Select
                        name="rentalUnit"
                        required={listingType === ListingType.RENT}
                      >
                        <SelectTrigger id="rentalUnit">
                          <SelectValue placeholder="Select rental unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {Object.entries(ListingRentalUnit).map(
                            ([key, value]) => (
                              <SelectItem key={key} value={value}>
                                {key.charAt(0).toUpperCase() +
                                  key.slice(1).toLowerCase()}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4">
                      <Label>Rental Availability</Label>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="startDate">Start Date</Label>
                          <Input
                            type="datetime-local"
                            id="startDate"
                            name="startDate"
                            min={formatDateForDatetimeLocal(
                              moment.utc().add(1, 'day')
                            )}
                            defaultValue={formatDateForDatetimeLocal(
                              defaultStartDate
                            )}
                            required={listingType === ListingType.RENT}
                            onChange={(e) => {
                              const newStartDate = moment.utc(e.target.value);
                              if (
                                newStartDate.isValid() &&
                                newStartDate.isAfter(defaultEndDate)
                              ) {
                                const newEndDate = moment
                                  .utc(newStartDate)
                                  .add(7, 'days');
                                setDefaultEndDate(newEndDate);
                                const endDateInput = document.getElementById(
                                  'endDate'
                                ) as HTMLInputElement;
                                if (endDateInput) {
                                  endDateInput.value =
                                    formatDateForDatetimeLocal(newEndDate);
                                }
                              }
                            }}
                          />
                        </div>
                        <div>
                          <Label htmlFor="endDate">End Date</Label>
                          <Input
                            type="datetime-local"
                            id="endDate"
                            name="endDate"
                            min={formatDateForDatetimeLocal(defaultStartDate)}
                            defaultValue={formatDateForDatetimeLocal(
                              defaultEndDate
                            )}
                            required={listingType === ListingType.RENT}
                          />
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        This defines when your item is available for rent. You
                        can add more availability periods later. Rental start
                        date must be at least tomorrow.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Item Condition - Show only when AI is off OR AI analysis is complete */}
              {(!useAI || aiResult) && (
                <Card>
                  <CardHeader>
                    <CardTitle>Item Condition</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="condition"
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel>Condition</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col space-y-1"
                            >
                              {Object.entries(ListingCondition).map(
                                ([key, value]) => (
                                  <FormItem
                                    key={key}
                                    className="flex items-center space-x-3 space-y-0"
                                  >
                                    <FormControl>
                                      <RadioGroupItem value={value} />
                                    </FormControl>
                                    <FormLabel className="font-normal">
                                      {key.charAt(0).toUpperCase() +
                                        key
                                          .slice(1)
                                          .toLowerCase()
                                          .replace('_', ' ')}
                                    </FormLabel>
                                  </FormItem>
                                )
                              )}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {featureFlags.marketplace.enableItemConditionSummary && (
                      <div>
                        <Label htmlFor="conditionSummary">
                          Condition Summary
                        </Label>
                        <Textarea
                          id="conditionSummary"
                          name="conditionSummary"
                          placeholder="Describe the condition"
                        />
                      </div>
                    )}

                    {featureFlags.marketplace
                      .enableItemConditionSafetyRequirements && (
                      <div>
                        <Label htmlFor="safetyRequirements">
                          Safety Requirements
                        </Label>
                        <Textarea
                          id="safetyRequirements"
                          name="safetyRequirements"
                          placeholder="List any safety requirements"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              <PhotoUpload
                photos={photos}
                setPhotos={setPhotos}
                isUploading={isUploading}
                uploadProgress={uploadProgress}
                useAI={useAI}
                aiResult={aiResult}
                validateFile={validateFile}
                setErrorMessage={setErrorMessage}
              />

              {/* Submit Button */}
              <div className="mt-6">
                <Button type="submit" disabled={isCreatingListing}>
                  {isCreatingListing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />{' '}
                      Creating...
                    </>
                  ) : (
                    'Create Listing'
                  )}
                </Button>
              </div>
            </div>
            {/* ListingGuidelines on the right for desktop, bottom for mobile/tablet */}
            <div className="hidden md:block">
              <ListingGuidelines />
            </div>
          </div>
          {/* Show ListingGuidelines at the bottom for mobile/tablet */}
          <div className="block mt-8 md:hidden">
            <ListingGuidelines />
          </div>
          {errorMessage && (
            <Alert
              variant="destructive"
              className="mt-6 duration-300 border-red-500 bg-red-50 dark:bg-red-900/20 animate-in fade-in slide-in-from-top-5"
            >
              <AlertCircle className="w-5 h-5 text-red-500" />
              <AlertTitle className="font-semibold text-red-700 dark:text-red-300">
                Error
              </AlertTitle>
              <AlertDescription className="text-red-600 dark:text-red-200">
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}
          {successMessage && (
            <Alert className="mt-6 duration-300 border-green-500 bg-green-50 dark:bg-green-900/20 animate-in fade-in slide-in-from-top-5">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <AlertTitle className="font-semibold text-green-700 dark:text-green-300">
                Success
              </AlertTitle>
              <AlertDescription className="text-green-600 dark:text-green-200">
                {successMessage}
              </AlertDescription>
            </Alert>
          )}
        </form>
      </Form>
    </div>
  );
}
