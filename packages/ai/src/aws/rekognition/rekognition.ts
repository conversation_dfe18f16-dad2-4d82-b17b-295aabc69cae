/**
 * AWS Rekognition Service for AI-powered image analysis
 * This service provides both mock and real AWS Rekognition functionality
 */

// ===== TYPE DEFINITIONS =====

export interface RekognitionLabel {
  Name: string;
  Confidence: number;
}

export interface RekognitionModerationLabel {
  Name: string;
  Confidence: number;
  ParentName?: string;
}

export interface RekognitionTextDetection {
  DetectedText: string;
  Confidence: number;
  Type: 'LINE' | 'WORD';
}

export interface RekognitionFaceDetail {
  BoundingBox: {
    Width: number;
    Height: number;
    Left: number;
    Top: number;
  };
  Confidence: number;
}

export interface RekognitionResponse {
  Labels: RekognitionLabel[];
  ModerationLabels: RekognitionModerationLabel[];
  TextDetections: RekognitionTextDetection[];
  FaceDetails: RekognitionFaceDetail[];
  Brand?: string;
  ConditionHint?: string;
}

export interface ProcessedAIResult {
  itemName: string;
  description: string;
  category: string;
  condition: string;
  detectedLabels: string[];
  detectedBrand?: string;
  contentWarning?: string;
  faceWarning?: string;
  textDetections: string[];
}

// ===== CONFIGURATION =====

export interface RekognitionConfig {
  useMockData?: boolean;
  region?: string;
  accessKeyId?: string;
  secretAccessKey?: string;
  confidenceThreshold?: {
    labels: number;
    text: number;
    moderation: number;
  };
}

const DEFAULT_CONFIG: Required<RekognitionConfig> = {
  useMockData: true,
  region: 'us-east-1',
  accessKeyId: '',
  secretAccessKey: '',
  confidenceThreshold: {
    labels: 80,
    text: 85,
    moderation: 75,
  },
};

// ===== MOCK DATA =====

const MOCK_RESPONSES: Record<string, RekognitionResponse> = {
  laptop: {
    Labels: [
      { Name: 'Laptop', Confidence: 95.5 },
      { Name: 'Computer', Confidence: 92.3 },
      { Name: 'Electronics', Confidence: 89.7 },
      { Name: 'Device', Confidence: 87.2 },
      { Name: 'Technology', Confidence: 85.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'MacBook Pro', Confidence: 98.2, Type: 'LINE' },
      { DetectedText: 'Model: A2338', Confidence: 94.5, Type: 'LINE' },
      { DetectedText: '13-inch', Confidence: 91.8, Type: 'WORD' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Good',
  },
  phone: {
    Labels: [
      { Name: 'Mobile Phone', Confidence: 97.8 },
      { Name: 'Smartphone', Confidence: 95.2 },
      { Name: 'Electronics', Confidence: 93.1 },
      { Name: 'Communication Device', Confidence: 88.9 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'iPhone 14', Confidence: 96.7, Type: 'LINE' },
      { DetectedText: 'Pro Max', Confidence: 94.3, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Apple',
    ConditionHint: 'Like New',
  },
  furniture: {
    Labels: [
      { Name: 'Furniture', Confidence: 94.2 },
      { Name: 'Chair', Confidence: 91.5 },
      { Name: 'Seat', Confidence: 88.7 },
      { Name: 'Wood', Confidence: 85.3 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  clothing: {
    Labels: [
      { Name: 'Clothing', Confidence: 96.1 },
      { Name: 'Apparel', Confidence: 93.4 },
      { Name: 'Shirt', Confidence: 89.8 },
      { Name: 'Fashion', Confidence: 87.2 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'Nike', Confidence: 92.5, Type: 'LINE' },
      { DetectedText: 'Size M', Confidence: 88.9, Type: 'LINE' },
    ],
    FaceDetails: [],
    Brand: 'Nike',
    ConditionHint: 'Good',
  },
  book: {
    Labels: [
      { Name: 'Book', Confidence: 98.5 },
      { Name: 'Publication', Confidence: 95.7 },
      { Name: 'Text', Confidence: 92.3 },
      { Name: 'Literature', Confidence: 89.1 },
    ],
    ModerationLabels: [],
    TextDetections: [
      { DetectedText: 'The Great Gatsby', Confidence: 97.2, Type: 'LINE' },
      { DetectedText: 'F. Scott Fitzgerald', Confidence: 94.8, Type: 'LINE' },
    ],
    FaceDetails: [],
    ConditionHint: 'Fair',
  },
  inappropriate: {
    Labels: [
      { Name: 'Person', Confidence: 95.2 },
      { Name: 'Human', Confidence: 92.8 },
    ],
    ModerationLabels: [
      { Name: 'Suggestive', Confidence: 87.5 },
      { Name: 'Explicit Nudity', Confidence: 82.3 },
    ],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.3, Height: 0.4, Left: 0.2, Top: 0.1 },
        Confidence: 94.7,
      },
    ],
  },
  violence: {
    Labels: [
      { Name: 'Weapon', Confidence: 91.4 },
      { Name: 'Violence', Confidence: 88.7 },
    ],
    ModerationLabels: [
      { Name: 'Violence', Confidence: 91.4 },
      { Name: 'Weapons', Confidence: 88.7 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  drugs: {
    Labels: [
      { Name: 'Drug Paraphernalia', Confidence: 89.3 },
      { Name: 'Substance', Confidence: 85.6 },
    ],
    ModerationLabels: [
      { Name: 'Drugs', Confidence: 89.3 },
      { Name: 'Drug Use', Confidence: 85.6 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  war: {
    Labels: [
      { Name: 'Military', Confidence: 92.1 },
      { Name: 'Weapon', Confidence: 88.4 },
      { Name: 'Combat', Confidence: 85.7 },
    ],
    ModerationLabels: [
      { Name: 'Violence', Confidence: 92.1 },
      { Name: 'Weapons', Confidence: 88.4 },
      { Name: 'Graphic Violence', Confidence: 85.7 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  nudity: {
    Labels: [
      { Name: 'Person', Confidence: 94.8 },
      { Name: 'Human', Confidence: 91.2 },
    ],
    ModerationLabels: [
      { Name: 'Explicit Nudity', Confidence: 94.8 },
      { Name: 'Graphic Male Nudity', Confidence: 91.2 },
      { Name: 'Graphic Female Nudity', Confidence: 88.5 },
    ],
    TextDetections: [],
    FaceDetails: [],
  },
  zeroAnalysis: {
    Labels: [],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [],
    ConditionHint: 'Unknown',
  },
  withFaces: {
    Labels: [
      { Name: 'Person', Confidence: 96.8 },
      { Name: 'Human', Confidence: 94.2 },
      { Name: 'Face', Confidence: 91.5 },
    ],
    ModerationLabels: [],
    TextDetections: [],
    FaceDetails: [
      {
        BoundingBox: { Width: 0.25, Height: 0.35, Left: 0.3, Top: 0.2 },
        Confidence: 96.8,
      },
      {
        BoundingBox: { Width: 0.22, Height: 0.32, Left: 0.6, Top: 0.25 },
        Confidence: 94.2,
      },
    ],
  },
};

// ===== CORE FUNCTIONS =====

/**
 * Analyzes an image using AWS Rekognition (or mock data)
 */
export async function analyzeImageWithRekognition(
  imageFile: File,
  config: RekognitionConfig = {}
): Promise<RekognitionResponse> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  if (finalConfig.useMockData) {
    return analyzeMockImage(imageFile);
  }

  // TODO: Implement real AWS Rekognition API calls
  throw new Error(
    'Real AWS Rekognition not implemented yet. Use mock data for now.'
  );
}

/**
 * Mock image analysis for development and testing
 */
async function analyzeMockImage(imageFile: File): Promise<RekognitionResponse> {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1500 + Math.random() * 1000)
  );

  // Determine mock response based on filename or random selection
  const fileName = imageFile.name.toLowerCase();

  if (
    fileName.includes('laptop') ||
    fileName.includes('macbook') ||
    fileName.includes('computer')
  ) {
    return MOCK_RESPONSES['laptop'];
  }
  if (
    fileName.includes('phone') ||
    fileName.includes('iphone') ||
    fileName.includes('mobile')
  ) {
    return MOCK_RESPONSES['phone'];
  }
  if (
    fileName.includes('chair') ||
    fileName.includes('furniture') ||
    fileName.includes('desk')
  ) {
    return MOCK_RESPONSES['furniture'];
  }
  if (
    fileName.includes('shirt') ||
    fileName.includes('clothing') ||
    fileName.includes('apparel')
  ) {
    return MOCK_RESPONSES['clothing'];
  }
  if (fileName.includes('book') || fileName.includes('novel')) {
    return MOCK_RESPONSES['book'];
  }
  if (fileName.includes('inappropriate') || fileName.includes('nsfw')) {
    return MOCK_RESPONSES['inappropriate'];
  }
  if (
    fileName.includes('face') ||
    fileName.includes('person') ||
    fileName.includes('people')
  ) {
    return MOCK_RESPONSES['withFaces'];
  }

  // Random selection for generic images with edge cases
  const random = Math.random();

  // 18% chance of moderated content (various types) - BLOCKS LISTING
  if (random < 0.18) {
    const moderatedTypes = [
      'inappropriate',
      'violence',
      'drugs',
      'war',
      'nudity',
    ];
    const randomType =
      moderatedTypes[Math.floor(Math.random() * moderatedTypes.length)];
    return MOCK_RESPONSES[randomType as keyof typeof MOCK_RESPONSES];
  }

  // 10% chance of zero analysis - NO DATA DETECTED
  if (random < 0.28) {
    return MOCK_RESPONSES['zeroAnalysis'];
  }

  // 7% chance of face detection
  if (random < 0.35) {
    return MOCK_RESPONSES['withFaces'];
  }

  // 10% chance of unrecognized/poor quality image
  if (random < 0.45) {
    return {
      Labels: [
        { Name: 'Object', Confidence: 45.2 },
        { Name: 'Item', Confidence: 38.7 },
        { Name: 'Unidentified', Confidence: 32.1 },
      ],
      ModerationLabels: [],
      TextDetections: [],
      FaceDetails: [],
      ConditionHint: 'Unknown',
    };
  }

  // Regular responses for remaining 55%
  const validResponses = [
    MOCK_RESPONSES['laptop'],
    MOCK_RESPONSES['phone'],
    MOCK_RESPONSES['furniture'],
    MOCK_RESPONSES['clothing'],
    MOCK_RESPONSES['book'],
  ];
  const randomIndex = Math.floor(Math.random() * validResponses.length);
  return validResponses[randomIndex];
}

/**
 * Processes raw Rekognition response into structured AI result
 */
export async function processRekognitionResponse(
  response: RekognitionResponse
): Promise<ProcessedAIResult> {
  // Extract top labels with confidence above threshold
  const topLabels = response.Labels.filter((label) => label.Confidence > 80)
    .map((label) => label.Name)
    .slice(0, 5);

  // Extract text detections
  const textDetections = response.TextDetections.filter(
    (text) => text.Confidence > 85 && text.Type === 'LINE'
  )
    .map((text) => text.DetectedText)
    .slice(0, 3);

  // Check for content moderation issues
  const contentWarning = checkContentModeration(response.ModerationLabels);

  // Check for face detection
  const faceWarning = checkFaceDetection(response.FaceDetails);

  // Check for different analysis scenarios
  const isZeroAnalysis = topLabels.length === 0 && textDetections.length === 0;
  const isUnrecognized =
    topLabels.includes('Object') && topLabels.includes('Unidentified');

  // Generate item name from top labels and text
  let itemName = '';
  if (isZeroAnalysis) {
    itemName = 'No Data Detected';
  } else if (isUnrecognized) {
    itemName = 'Unrecognized Item';
  } else if (textDetections.length > 0) {
    itemName = textDetections[0];
  } else if (topLabels.length > 0) {
    itemName = topLabels[0];
  }

  // Generate AI-powered description using mock Bedrock
  let description = '';
  if (isZeroAnalysis) {
    description =
      'No analyzable data was detected in this image. This could be due to poor lighting, blur, or an empty/unclear photo. Please try uploading a clearer image or fill in the details manually.';
  } else if (isUnrecognized) {
    description =
      'AI could not clearly identify this item from the image. Please manually enter the item details below for the best listing results.';
  } else {
    description = await generateBedrockDescription(
      topLabels,
      textDetections,
      response.Brand
    );
  }

  // Determine category from labels
  const category = determineCategory(topLabels);

  // Determine condition from hints or labels
  const condition = response.ConditionHint || determineCondition(topLabels);

  return {
    itemName,
    description,
    category,
    condition,
    detectedLabels: topLabels,
    detectedBrand: response.Brand,
    contentWarning,
    faceWarning,
    textDetections,
  };
}

// ===== HELPER FUNCTIONS =====

/**
 * Checks for content moderation issues
 */
function checkContentModeration(
  moderationLabels: RekognitionModerationLabel[]
): string | undefined {
  if (moderationLabels.length === 0) return undefined;

  const highConfidenceLabels = moderationLabels.filter(
    (label) => label.Confidence > 75
  );

  if (highConfidenceLabels.length === 0) return undefined;

  // Categorize moderation issues
  const nudityLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('nudity')
  );
  const violenceLabels = highConfidenceLabels.filter(
    (label) =>
      label.Name.toLowerCase().includes('violence') ||
      label.Name.toLowerCase().includes('weapon')
  );
  const drugLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('drug')
  );
  const suggestiveLabels = highConfidenceLabels.filter((label) =>
    label.Name.toLowerCase().includes('suggestive')
  );

  if (nudityLabels.length > 0) {
    return 'This image contains nudity or explicit content and cannot be used for marketplace listings. Please upload a different image that complies with our community guidelines.';
  }

  if (violenceLabels.length > 0) {
    return 'This image contains violent content, weapons, or war-related material and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (drugLabels.length > 0) {
    return 'This image contains drug-related content and cannot be used for marketplace listings. Please upload a different image.';
  }

  if (suggestiveLabels.length > 0) {
    return 'This image contains inappropriate or suggestive content and cannot be used for marketplace listings. Please upload a different image.';
  }

  // Generic moderation warning
  return `This image contains content that violates our community guidelines (${highConfidenceLabels[0].Name}) and cannot be used for marketplace listings.`;
}

/**
 * Checks for face detection and privacy concerns
 */
function checkFaceDetection(
  faceDetails: RekognitionFaceDetail[]
): string | undefined {
  if (faceDetails.length === 0) return undefined;

  const highConfidenceFaces = faceDetails.filter(
    (face) => face.Confidence > 90
  );

  if (highConfidenceFaces.length === 0) return undefined;

  if (highConfidenceFaces.length === 1) {
    return 'We detected a face in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.';
  }

  return `We detected ${highConfidenceFaces.length} faces in this image. For privacy reasons, consider using a photo that focuses on the item without showing people.`;
}

/**
 * Determines item category from detected labels
 */
function determineCategory(labels: string[]): string {
  const categoryMappings: Record<string, string[]> = {
    Electronics: [
      'Laptop',
      'Computer',
      'Mobile Phone',
      'Smartphone',
      'Electronics',
      'Device',
      'Technology',
    ],
    Furniture: ['Furniture', 'Chair', 'Table', 'Desk', 'Seat', 'Wood'],
    Clothing: ['Clothing', 'Apparel', 'Shirt', 'Fashion', 'Garment'],
    Books: ['Book', 'Publication', 'Text', 'Literature', 'Novel'],
    Sports: ['Sports', 'Equipment', 'Ball', 'Fitness', 'Exercise'],
    Toys: ['Toy', 'Game', 'Doll', 'Puzzle', 'Entertainment'],
    Home: ['Home', 'Kitchen', 'Appliance', 'Tool', 'Utensil'],
    Automotive: ['Car', 'Vehicle', 'Automotive', 'Transportation'],
  };

  for (const [category, keywords] of Object.entries(categoryMappings)) {
    if (labels.some((label) => keywords.includes(label))) {
      return category;
    }
  }

  return 'Other';
}

/**
 * Determines item condition from labels
 */
function determineCondition(labels: string[]): string {
  // Look for condition-related keywords in labels
  const conditionKeywords = {
    'Like New': ['new', 'pristine', 'mint', 'perfect'],
    Good: ['good', 'clean', 'working', 'functional'],
    Fair: ['used', 'worn', 'old', 'vintage'],
    Poor: ['damaged', 'broken', 'cracked', 'torn'],
  };

  for (const [condition, keywords] of Object.entries(conditionKeywords)) {
    if (
      labels.some((label) =>
        keywords.some((keyword) =>
          label.toLowerCase().includes(keyword.toLowerCase())
        )
      )
    ) {
      return condition;
    }
  }

  // Default condition based on item type
  if (labels.includes('Electronics') || labels.includes('Technology')) {
    return 'Good';
  }

  return 'Good'; // Default fallback
}

/**
 * Generates AI-powered description using mock Bedrock functionality
 */
async function generateBedrockDescription(
  labels: string[],
  textDetections: string[],
  brand?: string
): Promise<string> {
  // Simulate Bedrock API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 500 + Math.random() * 500)
  );

  // Build description based on detected elements
  let description = '';

  // Start with the main item
  if (labels.length > 0) {
    const mainItem = labels[0];
    description = `This ${mainItem.toLowerCase()}`;

    // Add brand if detected
    if (brand) {
      description += ` by ${brand}`;
    }

    // Add model/text information if available
    if (textDetections.length > 0) {
      const modelInfo = textDetections.join(', ');
      description += ` (${modelInfo})`;
    }

    // Add descriptive details based on other labels
    const additionalLabels = labels.slice(1, 3);
    if (additionalLabels.length > 0) {
      description += ` features ${additionalLabels
        .map((label) => label.toLowerCase())
        .join(' and ')} characteristics`;
    }

    // Add condition-based description
    description +=
      '. This item appears to be in good condition based on the image analysis';

    // Add call to action
    description +=
      '. Please review the details and condition before making your purchase decision.';
  } else {
    description =
      'Item details could not be automatically determined from the image. Please provide a detailed description manually.';
  }

  return description;
}

// ===== EXPORTS =====

export default {
  analyzeImageWithRekognition,
  processRekognitionResponse,
};
